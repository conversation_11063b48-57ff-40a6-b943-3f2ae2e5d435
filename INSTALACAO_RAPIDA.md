# 🚀 CrisisComm - Instalação Rápida

## ⚡ Instalação em 3 Passos

### 1️⃣ Instalar Dependências
```bash
pip install -r requirements.txt
```

### 2️⃣ Executar Configuração (Opcional)
```bash
python setup.py
```

### 3️⃣ Iniciar Aplicação
```bash
python run_crisiscomm.py
```

**OU**

```bash
streamlit run crisiscomm_app.py
```

## 🌐 Acesso

Abra no navegador: **http://localhost:8501**

---

## 🐳 Instalação com Docker

### Opção 1: Docker Compose (Recomendado)
```bash
docker-compose up -d
```

### Opção 2: Docker Manual
```bash
# Build
docker build -t crisiscomm .

# Run
docker run -p 8501:8501 crisiscomm
```

---

## 🔧 Solução Rápida de Problemas

### ❌ Erro: "Modelo não encontrado"
- **Solução**: Aguarde o download automático na primeira execução
- **Tempo**: 5-10 minutos (dependendo da conexão)

### ❌ Erro: "Memória insuficiente"
- **Solução**: Configure `DEVICE=cpu` no arquivo `.env`
- **Alternativa**: Feche outros programas

### ❌ Erro: "Streamlit não encontrado"
```bash
pip install streamlit
```

### ❌ Erro: "Transformers não encontrado"
```bash
pip install transformers torch
```

---

## 📋 Requisitos Mínimos

- **Python**: 3.8+
- **RAM**: 4GB (8GB recomendado)
- **Espaço**: 5GB livres
- **Internet**: Para download inicial do modelo

---

## 🎯 Teste Rápido

1. Acesse a aba **"📚 Exemplos de Demonstração"**
2. Copie um texto de exemplo
3. Cole na aba **"🗣️ Tradutor de Emergência"**
4. Clique em **"🔄 Traduzir"**

---

## 🆘 Suporte Rápido

- **GitHub Issues**: [Reportar Problema](https://github.com/seu-usuario/crisiscomm/issues)
- **Email**: <EMAIL>
- **Documentação**: [README_CRISISCOMM.md](README_CRISISCOMM.md)

---

**🚨 Pronto para salvar vidas em emergências!**
