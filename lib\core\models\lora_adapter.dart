import 'package:json_annotation/json_annotation.dart';

part 'lora_adapter.g.dart';

/// LoRA (Low-Rank Adaptation) adapter model for domain specialization
@JsonSerializable()
class LoRAAdapter {
  final String id;
  final String name;
  final String domain;
  final String description;
  final String version;
  final String downloadUrl;
  final List<String> supportedLanguages;
  final int size; // Size in bytes
  final double accuracy; // Accuracy score 0.0-1.0
  
  bool isInstalled;
  bool isActive;
  DateTime? installDate;
  DateTime? lastUsed;
  Map<String, dynamic>? metadata;

  LoRAAdapter({
    required this.id,
    required this.name,
    required this.domain,
    required this.description,
    required this.version,
    required this.downloadUrl,
    required this.supportedLanguages,
    required this.size,
    required this.accuracy,
    this.isInstalled = false,
    this.isActive = false,
    this.installDate,
    this.lastUsed,
    this.metadata,
  });

  /// Create from JSON
  factory LoRAAdapter.from<PERSON>son(Map<String, dynamic> json) =>
      _$LoRAAdapterFrom<PERSON><PERSON>(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$LoRAAdapterToJson(this);

  /// Create a copy with modified fields
  LoRAAdapter copyWith({
    String? id,
    String? name,
    String? domain,
    String? description,
    String? version,
    String? downloadUrl,
    List<String>? supportedLanguages,
    int? size,
    double? accuracy,
    bool? isInstalled,
    bool? isActive,
    DateTime? installDate,
    DateTime? lastUsed,
    Map<String, dynamic>? metadata,
  }) {
    return LoRAAdapter(
      id: id ?? this.id,
      name: name ?? this.name,
      domain: domain ?? this.domain,
      description: description ?? this.description,
      version: version ?? this.version,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      supportedLanguages: supportedLanguages ?? this.supportedLanguages,
      size: size ?? this.size,
      accuracy: accuracy ?? this.accuracy,
      isInstalled: isInstalled ?? this.isInstalled,
      isActive: isActive ?? this.isActive,
      installDate: installDate ?? this.installDate,
      lastUsed: lastUsed ?? this.lastUsed,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get formatted size string
  String get formattedSize {
    if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Get accuracy percentage
  String get accuracyPercentage => '${(accuracy * 100).toStringAsFixed(1)}%';

  /// Get domain display name
  String get domainDisplayName {
    switch (domain.toLowerCase()) {
      case 'medical':
        return 'Médico';
      case 'legal':
        return 'Jurídico';
      case 'technical':
        return 'Técnico';
      case 'business':
        return 'Negócios';
      case 'casual':
        return 'Casual';
      case 'academic':
        return 'Acadêmico';
      case 'scientific':
        return 'Científico';
      case 'literary':
        return 'Literário';
      default:
        return domain.toUpperCase();
    }
  }

  /// Get status display string
  String get statusDisplay {
    if (!isInstalled) return 'Não instalado';
    if (isActive) return 'Ativo';
    return 'Instalado';
  }

  /// Get install date formatted
  String? get formattedInstallDate {
    if (installDate == null) return null;
    return '${installDate!.day}/${installDate!.month}/${installDate!.year}';
  }

  /// Get last used formatted
  String? get formattedLastUsed {
    if (lastUsed == null) return 'Nunca usado';
    
    final now = DateTime.now();
    final difference = now.difference(lastUsed!);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} dias atrás';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} horas atrás';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutos atrás';
    } else {
      return 'Agora mesmo';
    }
  }

  /// Check if adapter supports a specific language
  bool supportsLanguage(String languageCode) {
    return supportedLanguages.contains(languageCode);
  }

  /// Get compatibility score for language pair
  double getCompatibilityScore(String sourceLanguage, String targetLanguage) {
    double score = 0.0;
    
    // Base score for domain specialization
    score += 0.3;
    
    // Language support bonus
    if (supportsLanguage(sourceLanguage)) score += 0.3;
    if (supportsLanguage(targetLanguage)) score += 0.3;
    
    // Accuracy bonus
    score += accuracy * 0.1;
    
    return score.clamp(0.0, 1.0);
  }

  /// Get recommended use cases
  List<String> get recommendedUseCases {
    switch (domain.toLowerCase()) {
      case 'medical':
        return [
          'Documentos médicos',
          'Prescrições',
          'Relatórios clínicos',
          'Artigos científicos médicos',
        ];
      case 'legal':
        return [
          'Contratos',
          'Documentos jurídicos',
          'Legislação',
          'Pareceres legais',
        ];
      case 'technical':
        return [
          'Manuais técnicos',
          'Documentação de software',
          'Especificações',
          'Guias de instalação',
        ];
      case 'business':
        return [
          'Emails corporativos',
          'Relatórios empresariais',
          'Apresentações',
          'Propostas comerciais',
        ];
      case 'casual':
        return [
          'Conversas informais',
          'Redes sociais',
          'Mensagens pessoais',
          'Blogs e fóruns',
        ];
      default:
        return ['Uso geral'];
    }
  }

  @override
  String toString() {
    return 'LoRAAdapter(id: $id, name: $name, domain: $domain, '
           'isInstalled: $isInstalled, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoRAAdapter &&
        other.id == id &&
        other.version == version;
  }

  @override
  int get hashCode => id.hashCode ^ version.hashCode;
}
