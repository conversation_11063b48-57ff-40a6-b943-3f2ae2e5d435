import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

import '../app_config.dart';

/// Enhanced logging utility with different log levels and formatting
class Logger {
  static const String _name = 'SimulTransAI';
  
  // ANSI color codes for console output
  static const String _reset = '\x1B[0m';
  static const String _red = '\x1B[31m';
  static const String _green = '\x1B[32m';
  static const String _yellow = '\x1B[33m';
  static const String _blue = '\x1B[34m';
  static const String _magenta = '\x1B[35m';
  static const String _cyan = '\x1B[36m';
  static const String _white = '\x1B[37m';
  static const String _bold = '\x1B[1m';

  /// Log debug message (only in debug mode)
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    if (!AppConfig.isDebugMode) return;
    
    _log(
      level: LogLevel.debug,
      message: message,
      error: error,
      stackTrace: stackTrace,
      color: _cyan,
    );
  }

  /// Log info message
  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    _log(
      level: LogLevel.info,
      message: message,
      error: error,
      stackTrace: stackTrace,
      color: _green,
    );
  }

  /// Log warning message
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    _log(
      level: LogLevel.warning,
      message: message,
      error: error,
      stackTrace: stackTrace,
      color: _yellow,
    );
  }

  /// Log error message
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    _log(
      level: LogLevel.error,
      message: message,
      error: error,
      stackTrace: stackTrace,
      color: _red,
    );
  }

  /// Log critical error message
  static void critical(String message, [Object? error, StackTrace? stackTrace]) {
    _log(
      level: LogLevel.critical,
      message: message,
      error: error,
      stackTrace: stackTrace,
      color: _red + _bold,
    );
  }

  /// Log performance message
  static void performance(String message, Duration duration, [Map<String, dynamic>? metadata]) {
    if (!AppConfig.enablePerformanceProfiling) return;
    
    final metadataStr = metadata != null ? ' | ${metadata.toString()}' : '';
    _log(
      level: LogLevel.performance,
      message: '$message (${duration.inMilliseconds}ms)$metadataStr',
      color: _magenta,
    );
  }

  /// Log network operation
  static void network(String operation, String url, {
    Duration? duration,
    int? statusCode,
    Object? error,
  }) {
    final durationStr = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    final statusStr = statusCode != null ? ' [$statusCode]' : '';
    final errorStr = error != null ? ' ERROR: $error' : '';
    
    _log(
      level: LogLevel.network,
      message: '$operation $url$statusStr$durationStr$errorStr',
      color: _blue,
    );
  }

  /// Log Gemma operation
  static void gemma(String operation, {
    String? modelName,
    Duration? duration,
    Map<String, dynamic>? metadata,
    Object? error,
  }) {
    final modelStr = modelName != null ? ' [$modelName]' : '';
    final durationStr = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    final metadataStr = metadata != null ? ' | ${metadata.toString()}' : '';
    final errorStr = error != null ? ' ERROR: $error' : '';
    
    _log(
      level: LogLevel.gemma,
      message: '$operation$modelStr$durationStr$metadataStr$errorStr',
      color: _white + _bold,
    );
  }

  /// Log cache operation
  static void cache(String operation, {
    String? key,
    int? size,
    bool? hit,
    Duration? duration,
  }) {
    final keyStr = key != null ? ' [$key]' : '';
    final sizeStr = size != null ? ' (${_formatBytes(size)})' : '';
    final hitStr = hit != null ? (hit ? ' HIT' : ' MISS') : '';
    final durationStr = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    
    _log(
      level: LogLevel.cache,
      message: '$operation$keyStr$hitStr$sizeStr$durationStr',
      color: _cyan,
    );
  }

  /// Log translation operation
  static void translation(String operation, {
    String? sourceLanguage,
    String? targetLanguage,
    int? textLength,
    Duration? duration,
    double? confidence,
    Object? error,
  }) {
    final langStr = sourceLanguage != null && targetLanguage != null 
        ? ' [$sourceLanguage → $targetLanguage]' 
        : '';
    final lengthStr = textLength != null ? ' (${textLength} chars)' : '';
    final durationStr = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    final confidenceStr = confidence != null ? ' [${(confidence * 100).toStringAsFixed(1)}%]' : '';
    final errorStr = error != null ? ' ERROR: $error' : '';
    
    _log(
      level: LogLevel.translation,
      message: '$operation$langStr$lengthStr$durationStr$confidenceStr$errorStr',
      color: _green + _bold,
    );
  }

  /// Format bytes to human readable format
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Internal logging method
  static void _log({
    required LogLevel level,
    required String message,
    Object? error,
    StackTrace? stackTrace,
    String color = '',
  }) {
    if (!_shouldLog(level)) return;

    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.name.toUpperCase().padRight(11);
    
    // Format the log message
    String logMessage;
    if (kDebugMode && color.isNotEmpty) {
      // Colored output for debug mode
      logMessage = '$color[$timestamp] $levelStr | $message$_reset';
    } else {
      // Plain output for release mode
      logMessage = '[$timestamp] $levelStr | $message';
    }

    // Add error information if provided
    if (error != null) {
      logMessage += '\nError: $error';
    }

    // Add stack trace if provided and in verbose mode
    if (stackTrace != null && AppConfig.enableVerboseLogging) {
      logMessage += '\nStack trace:\n$stackTrace';
    }

    // Output the log
    if (kDebugMode) {
      // Use developer.log for better debugging experience
      developer.log(
        message,
        time: DateTime.now(),
        level: _getLogLevelValue(level),
        name: _name,
        error: error,
        stackTrace: stackTrace,
      );
      
      // Also print to console for immediate visibility
      if (AppConfig.enableVerboseLogging) {
        print(logMessage);
      }
    } else {
      // In release mode, use print for basic logging
      print(logMessage);
    }
  }

  /// Check if we should log at this level
  static bool _shouldLog(LogLevel level) {
    if (!AppConfig.isDebugMode && level == LogLevel.debug) {
      return false;
    }
    
    if (!AppConfig.enableVerboseLogging && level == LogLevel.debug) {
      return false;
    }
    
    return true;
  }

  /// Get numeric log level for developer.log
  static int _getLogLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.critical:
        return 1200;
      case LogLevel.performance:
        return 700;
      case LogLevel.network:
        return 600;
      case LogLevel.gemma:
        return 750;
      case LogLevel.cache:
        return 550;
      case LogLevel.translation:
        return 800;
    }
  }
}

/// Log levels for different types of messages
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
  performance,
  network,
  gemma,
  cache,
  translation,
}
