import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/models/language_info.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/analytics_service.dart';

/// Advanced language selector with search, favorites, and recent languages
class LanguageSelector extends ConsumerStatefulWidget {
  final String sourceLanguage;
  final String targetLanguage;
  final Function(String) onSourceLanguageChanged;
  final Function(String) onTargetLanguageChanged;
  final VoidCallback onSwapLanguages;

  const LanguageSelector({
    super.key,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.onSourceLanguageChanged,
    required this.onTargetLanguageChanged,
    required this.onSwapLanguages,
  });

  @override
  ConsumerState<LanguageSelector> createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends ConsumerState<LanguageSelector>
    with TickerProviderStateMixin {
  late AnimationController _swapController;
  late Animation<double> _swapAnimation;

  @override
  void initState() {
    super.initState();
    _swapController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _swapAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _swapController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _swapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final supportedLanguages = ref.watch(supportedLanguagesProvider);
    final languagePreferences = ref.watch(languagePreferencesProvider);

    final sourceLanguageInfo = LanguageInfo.getLanguageInfo(widget.sourceLanguage);
    final targetLanguageInfo = LanguageInfo.getLanguageInfo(widget.targetLanguage);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.translate,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Idiomas',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Language Selection Row
            Row(
              children: [
                // Source Language
                Expanded(
                  child: _LanguageButton(
                    language: sourceLanguageInfo,
                    isSource: true,
                    onTap: () => _showLanguagePicker(
                      context,
                      isSource: true,
                      currentLanguage: widget.sourceLanguage,
                      supportedLanguages: supportedLanguages,
                      languagePreferences: languagePreferences,
                      onLanguageSelected: widget.onSourceLanguageChanged,
                    ),
                  ),
                ),

                // Swap Button
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  child: AnimatedBuilder(
                    animation: _swapAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _swapAnimation.value * 3.14159, // 180 degrees
                        child: IconButton(
                          onPressed: _handleSwapLanguages,
                          icon: const Icon(Icons.swap_horiz),
                          style: IconButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                            foregroundColor: AppTheme.primaryColor,
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Target Language
                Expanded(
                  child: _LanguageButton(
                    language: targetLanguageInfo,
                    isSource: false,
                    onTap: () => _showLanguagePicker(
                      context,
                      isSource: false,
                      currentLanguage: widget.targetLanguage,
                      supportedLanguages: supportedLanguages,
                      languagePreferences: languagePreferences,
                      onLanguageSelected: widget.onTargetLanguageChanged,
                    ),
                  ),
                ),
              ],
            ),

            // Language Info
            if (sourceLanguageInfo != null || targetLanguageInfo != null) ...[
              const SizedBox(height: 12),
              _buildLanguageInfo(sourceLanguageInfo, targetLanguageInfo),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageInfo(LanguageInfo? source, LanguageInfo? target) {
    final theme = Theme.of(context);
    final infoItems = <String>[];

    if (source?.isTonal == true || target?.isTonal == true) {
      infoItems.add('Idioma tonal');
    }
    if (source?.hasComplexScript == true || target?.hasComplexScript == true) {
      infoItems.add('Script complexo');
    }
    if (source?.isRightToLeft == true || target?.isRightToLeft == true) {
      infoItems.add('Direita para esquerda');
    }

    if (infoItems.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.infoColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.infoColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: AppTheme.infoColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              infoItems.join(' • '),
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppTheme.infoColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleSwapLanguages() {
    if (widget.sourceLanguage != 'auto') {
      _swapController.forward().then((_) {
        _swapController.reset();
      });
      widget.onSwapLanguages();
      
      AnalyticsService.instance.trackFeatureUsage(
        featureName: 'language_swap',
        parameters: {
          'from': widget.sourceLanguage,
          'to': widget.targetLanguage,
        },
      );
    }
  }

  void _showLanguagePicker(
    BuildContext context, {
    required bool isSource,
    required String currentLanguage,
    required List<LanguageInfo> supportedLanguages,
    required LanguagePreferences languagePreferences,
    required Function(String) onLanguageSelected,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _LanguagePickerModal(
        isSource: isSource,
        currentLanguage: currentLanguage,
        supportedLanguages: supportedLanguages,
        languagePreferences: languagePreferences,
        onLanguageSelected: onLanguageSelected,
      ),
    );
  }
}

class _LanguageButton extends StatelessWidget {
  final LanguageInfo? language;
  final bool isSource;
  final VoidCallback onTap;

  const _LanguageButton({
    required this.language,
    required this.isSource,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isSource ? 'De' : 'Para',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language?.name ?? 'Selecionar',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (language != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          language!.nativeName,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _LanguagePickerModal extends ConsumerStatefulWidget {
  final bool isSource;
  final String currentLanguage;
  final List<LanguageInfo> supportedLanguages;
  final LanguagePreferences languagePreferences;
  final Function(String) onLanguageSelected;

  const _LanguagePickerModal({
    required this.isSource,
    required this.currentLanguage,
    required this.supportedLanguages,
    required this.languagePreferences,
    required this.onLanguageSelected,
  });

  @override
  ConsumerState<_LanguagePickerModal> createState() => _LanguagePickerModalState();
}

class _LanguagePickerModalState extends ConsumerState<_LanguagePickerModal> {
  final TextEditingController _searchController = TextEditingController();
  List<LanguageInfo> _filteredLanguages = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _filteredLanguages = widget.supportedLanguages;
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      if (_searchQuery.isEmpty) {
        _filteredLanguages = widget.supportedLanguages;
      } else {
        _filteredLanguages = LanguageInfo.searchLanguages(_searchQuery);
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final popularLanguages = LanguageInfo.getPopularLanguages();
    final recentLanguages = widget.languagePreferences.recentLanguages
        .map((code) => LanguageInfo.getLanguageInfo(code))
        .where((lang) => lang != null)
        .cast<LanguageInfo>()
        .toList();
    final favoriteLanguages = widget.languagePreferences.favoriteLanguages
        .map((code) => LanguageInfo.getLanguageInfo(code))
        .where((lang) => lang != null)
        .cast<LanguageInfo>()
        .toList();

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.isSource ? 'Idioma de origem' : 'Idioma de destino',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Search
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Buscar idioma...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
              ),
            ),
          ),

          // Language Lists
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Auto-detect (only for source)
                if (widget.isSource) ...[
                  _buildLanguageSection(
                    'Detecção Automática',
                    [LanguageInfo.getLanguageInfo('auto')!],
                  ),
                  const SizedBox(height: 16),
                ],

                // Recent Languages
                if (recentLanguages.isNotEmpty && _searchQuery.isEmpty) ...[
                  _buildLanguageSection('Recentes', recentLanguages),
                  const SizedBox(height: 16),
                ],

                // Favorite Languages
                if (favoriteLanguages.isNotEmpty && _searchQuery.isEmpty) ...[
                  _buildLanguageSection('Favoritos', favoriteLanguages),
                  const SizedBox(height: 16),
                ],

                // Popular Languages
                if (_searchQuery.isEmpty) ...[
                  _buildLanguageSection('Populares', popularLanguages),
                  const SizedBox(height: 16),
                ],

                // All Languages
                _buildLanguageSection(
                  _searchQuery.isEmpty ? 'Todos os idiomas' : 'Resultados da busca',
                  _filteredLanguages,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSection(String title, List<LanguageInfo> languages) {
    if (languages.isEmpty) return const SizedBox.shrink();

    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        ...languages.map((language) => _buildLanguageItem(language)),
      ],
    );
  }

  Widget _buildLanguageItem(LanguageInfo language) {
    final theme = Theme.of(context);
    final isSelected = language.code == widget.currentLanguage;
    final isFavorite = widget.languagePreferences.favoriteLanguages.contains(language.code);

    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected 
              ? AppTheme.primaryColor 
              : AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            language.code.toUpperCase(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: isSelected ? Colors.white : AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      title: Text(
        language.name,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: Text(language.nativeName),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (language.isTonal)
            Icon(
              Icons.music_note,
              size: 16,
              color: Colors.grey[600],
            ),
          if (language.hasComplexScript)
            Icon(
              Icons.text_format,
              size: 16,
              color: Colors.grey[600],
            ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => _toggleFavorite(language.code),
            icon: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? Colors.red : Colors.grey[600],
              size: 20,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onTap: () => _selectLanguage(language.code),
    );
  }

  void _selectLanguage(String languageCode) {
    widget.onLanguageSelected(languageCode);
    Navigator.of(context).pop();
    
    AnalyticsService.instance.trackFeatureUsage(
      featureName: 'language_selection',
      parameters: {
        'language': languageCode,
        'is_source': widget.isSource,
        'search_query': _searchQuery,
      },
    );
  }

  void _toggleFavorite(String languageCode) {
    if (widget.languagePreferences.favoriteLanguages.contains(languageCode)) {
      ref.read(languagePreferencesProvider.notifier).removeFromFavorites(languageCode);
    } else {
      ref.read(languagePreferencesProvider.notifier).addToFavorites(languageCode);
    }
  }
}
