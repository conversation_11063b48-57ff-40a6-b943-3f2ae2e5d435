import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';

/// Status indicator showing Gemma 3N initialization and health status
class StatusIndicator extends ConsumerWidget {
  final AsyncValue<bool> gemmaStatus;

  const StatusIndicator({
    super.key,
    required this.gemmaStatus,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return gemmaStatus.when(
      data: (isInitialized) => _buildStatusChip(
        context,
        isInitialized ? 'Online' : 'Offline',
        isInitialized ? Colors.green : Colors.red,
        isInitialized ? Icons.check_circle : Icons.error,
      ),
      loading: () => _buildStatusChip(
        context,
        'Carregando',
        Colors.orange,
        Icons.hourglass_empty,
        isLoading: true,
      ),
      error: (error, stack) => _buildStatusChip(
        context,
        'Erro',
        Colors.red,
        Icons.error,
      ),
    );
  }

  Widget _buildStatusChip(
    BuildContext context,
    String label,
    Color color,
    IconData icon, {
    bool isLoading = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isLoading)
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else
            Icon(
              icon,
              size: 12,
              color: Colors.white,
            ),
          const SizedBox(width: 6),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
