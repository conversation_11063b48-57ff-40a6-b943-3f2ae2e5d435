"""
Testes para CrisisComm
"""

import pytest
import tempfile
import os
from PIL import Image
import numpy as np
from unittest.mock import Mock, patch
import json

# Importar módulos do projeto
from config import CrisisCommConfig, EnvironmentConfig, AppConstants
from utils import FileUtils, ImageUtils, TextUtils, ValidationUtils
from demo_examples import DemoExamples

class TestCrisisCommConfig:
    """Testes para configurações"""
    
    def test_supported_languages(self):
        """Testa idiomas suportados"""
        assert "en" in CrisisCommConfig.SUPPORTED_LANGUAGES
        assert "es" in CrisisCommConfig.SUPPORTED_LANGUAGES
        assert "ar" in CrisisCommConfig.SUPPORTED_LANGUAGES
        
        # Testa idioma RTL
        assert CrisisCommConfig.is_rtl_language("ar") == True
        assert CrisisCommConfig.is_rtl_language("en") == False
    
    def test_emergency_templates(self):
        """Testa templates de emergência"""
        assert "medical" in CrisisCommConfig.EMERGENCY_TEMPLATES
        assert "fire" in CrisisCommConfig.EMERGENCY_TEMPLATES
        
        medical_template = CrisisCommConfig.get_emergency_template("medical")
        assert medical_template["priority"] == "high"
        assert medical_template["icon"] == "🏥"
    
    def test_language_display_name(self):
        """Testa nomes de exibição dos idiomas"""
        display_name = CrisisCommConfig.get_language_display_name("en")
        assert "English" in display_name
        
        # Testa idioma inexistente
        unknown_display = CrisisCommConfig.get_language_display_name("xyz")
        assert "xyz" in unknown_display
    
    def test_urgency_levels(self):
        """Testa níveis de urgência"""
        critical = CrisisCommConfig.get_urgency_info("critical")
        assert critical["color"] == "#dc3545"
        assert critical["icon"] == "🔴"
        
        # Testa nível inexistente (deve retornar médio)
        unknown = CrisisCommConfig.get_urgency_info("unknown")
        assert unknown["name"] == "Média"

class TestFileUtils:
    """Testes para utilitários de arquivo"""
    
    def test_validate_file_size(self):
        """Testa validação de tamanho de arquivo"""
        # Mock de arquivo pequeno
        small_file = Mock()
        small_file.getvalue.return_value = b"x" * 1024  # 1KB
        assert FileUtils.validate_file_size(small_file, max_size_mb=1) == True
        
        # Mock de arquivo grande
        large_file = Mock()
        large_file.getvalue.return_value = b"x" * (2 * 1024 * 1024)  # 2MB
        assert FileUtils.validate_file_size(large_file, max_size_mb=1) == False
        
        # Arquivo None
        assert FileUtils.validate_file_size(None) == False
    
    def test_validate_file_type(self):
        """Testa validação de tipo de arquivo"""
        # Mock de arquivo válido
        valid_file = Mock()
        valid_file.name = "test.jpg"
        assert FileUtils.validate_file_type(valid_file, ["jpg", "png"]) == True
        
        # Mock de arquivo inválido
        invalid_file = Mock()
        invalid_file.name = "test.txt"
        assert FileUtils.validate_file_type(invalid_file, ["jpg", "png"]) == False
        
        # Arquivo None
        assert FileUtils.validate_file_type(None, ["jpg"]) == False
    
    def test_save_temp_file(self):
        """Testa salvamento de arquivo temporário"""
        # Mock de arquivo
        mock_file = Mock()
        mock_file.getvalue.return_value = b"test content"
        mock_file.name = "test.txt"
        
        temp_path = FileUtils.save_temp_file(mock_file)
        
        if temp_path:  # Se não houve erro
            assert os.path.exists(temp_path)
            
            # Limpar arquivo temporário
            FileUtils.cleanup_temp_file(temp_path)
            assert not os.path.exists(temp_path)

class TestImageUtils:
    """Testes para utilitários de imagem"""
    
    def create_test_image(self, size=(100, 100), mode="RGB"):
        """Cria imagem de teste"""
        return Image.new(mode, size, color="red")
    
    def test_resize_image(self):
        """Testa redimensionamento de imagem"""
        image = self.create_test_image((2000, 2000))
        resized = ImageUtils.resize_image(image, max_size=(1024, 1024))
        
        assert resized.size[0] <= 1024
        assert resized.size[1] <= 1024
    
    def test_optimize_image_for_model(self):
        """Testa otimização de imagem para modelo"""
        # Imagem RGBA
        rgba_image = self.create_test_image((300, 300), mode="RGBA")
        optimized = ImageUtils.optimize_image_for_model(rgba_image)
        
        assert optimized.mode == "RGB"
        assert optimized.size in [(256, 256), (512, 512), (768, 768)]
    
    def test_extract_image_metadata(self):
        """Testa extração de metadados"""
        image = self.create_test_image((100, 100))
        metadata = ImageUtils.extract_image_metadata(image)
        
        assert "size" in metadata
        assert "mode" in metadata
        assert metadata["size"] == (100, 100)
        assert metadata["mode"] == "RGB"
    
    def test_detect_image_quality(self):
        """Testa detecção de qualidade"""
        # Imagem pequena
        small_image = self.create_test_image((100, 100))
        assert ImageUtils.detect_image_quality(small_image) == "baixa"
        
        # Imagem média
        medium_image = self.create_test_image((800, 600))
        assert ImageUtils.detect_image_quality(medium_image) == "média"
        
        # Imagem grande
        large_image = self.create_test_image((2000, 2000))
        assert ImageUtils.detect_image_quality(large_image) == "alta"

class TestTextUtils:
    """Testes para utilitários de texto"""
    
    def test_clean_text(self):
        """Testa limpeza de texto"""
        dirty_text = "  Texto   com    espaços   extras  \n\t"
        clean = TextUtils.clean_text(dirty_text)
        assert clean == "Texto com espaços extras"
        
        # Texto vazio
        assert TextUtils.clean_text("") == ""
        assert TextUtils.clean_text(None) == ""
    
    def test_truncate_text(self):
        """Testa truncamento de texto"""
        long_text = "a" * 1000
        truncated = TextUtils.truncate_text(long_text, max_length=100)
        
        assert len(truncated) <= 100
        assert truncated.endswith("...")
        
        # Texto curto não deve ser truncado
        short_text = "texto curto"
        assert TextUtils.truncate_text(short_text, max_length=100) == short_text
    
    def test_detect_language_hints(self):
        """Testa detecção de idiomas"""
        # Texto em árabe
        arabic_text = "مرحبا بكم"
        hints = TextUtils.detect_language_hints(arabic_text)
        assert "ar" in hints
        
        # Texto em chinês
        chinese_text = "你好世界"
        hints = TextUtils.detect_language_hints(chinese_text)
        assert "zh" in hints
        
        # Texto em inglês (sem hints específicos)
        english_text = "Hello world"
        hints = TextUtils.detect_language_hints(english_text)
        assert len(hints) == 0
    
    def test_format_emergency_message(self):
        """Testa formatação de mensagem de emergência"""
        template = "Emergência em {location}. Tipo: {type}."
        data = {"location": "São Paulo", "type": "incêndio"}
        
        formatted = TextUtils.format_emergency_message(template, data)
        assert "São Paulo" in formatted
        assert "incêndio" in formatted
        
        # Dados incompletos
        incomplete_data = {"location": "São Paulo"}
        result = TextUtils.format_emergency_message(template, incomplete_data)
        assert result == template  # Deve retornar template original

class TestValidationUtils:
    """Testes para utilitários de validação"""
    
    def test_validate_coordinates(self):
        """Testa validação de coordenadas"""
        # Coordenadas válidas
        assert ValidationUtils.validate_coordinates(0, 0) == True
        assert ValidationUtils.validate_coordinates(-23.5505, -46.6333) == True  # São Paulo
        
        # Coordenadas inválidas
        assert ValidationUtils.validate_coordinates(91, 0) == False  # Latitude > 90
        assert ValidationUtils.validate_coordinates(0, 181) == False  # Longitude > 180
        assert ValidationUtils.validate_coordinates(-91, 0) == False  # Latitude < -90
    
    def test_validate_phone_number(self):
        """Testa validação de telefone"""
        # Números válidos
        assert ValidationUtils.validate_phone_number("+5511999999999") == True
        assert ValidationUtils.validate_phone_number("11999999999") == True
        
        # Números inválidos
        assert ValidationUtils.validate_phone_number("abc") == False
        assert ValidationUtils.validate_phone_number("") == False
        assert ValidationUtils.validate_phone_number("123") == False
    
    def test_validate_email(self):
        """Testa validação de email"""
        # Emails válidos
        assert ValidationUtils.validate_email("<EMAIL>") == True
        assert ValidationUtils.validate_email("<EMAIL>") == True
        
        # Emails inválidos
        assert ValidationUtils.validate_email("invalid-email") == False
        assert ValidationUtils.validate_email("@domain.com") == False
        assert ValidationUtils.validate_email("user@") == False
    
    def test_sanitize_input(self):
        """Testa sanitização de entrada"""
        dangerous_input = "<script>alert('xss')</script>"
        sanitized = ValidationUtils.sanitize_input(dangerous_input)
        
        assert "<" not in sanitized
        assert ">" not in sanitized
        assert "script" in sanitized  # Conteúdo deve permanecer
        
        # Entrada vazia
        assert ValidationUtils.sanitize_input("") == ""
        assert ValidationUtils.sanitize_input(None) == ""

class TestDemoExamples:
    """Testes para exemplos de demonstração"""
    
    def test_demo_examples_initialization(self):
        """Testa inicialização dos exemplos"""
        demo = DemoExamples()
        
        assert "translation" in demo.examples
        assert "emergency_scenarios" in demo.examples
        assert "multilingual_messages" in demo.examples
    
    def test_translation_examples(self):
        """Testa exemplos de tradução"""
        demo = DemoExamples()
        translation_examples = demo.get_translation_examples()
        
        assert "medical_emergency" in translation_examples
        assert "fire_emergency" in translation_examples
        
        # Verifica se tem múltiplos idiomas
        medical = translation_examples["medical_emergency"]
        assert "en" in medical
        assert "es" in medical
        assert "pt" in medical
    
    def test_emergency_scenarios(self):
        """Testa cenários de emergência"""
        demo = DemoExamples()
        scenarios = demo.get_emergency_scenarios()
        
        assert "scenario_1" in scenarios
        
        scenario = scenarios["scenario_1"]
        assert "title" in scenario
        assert "location" in scenario
        assert "languages" in scenario
        assert "input_text" in scenario

class TestEnvironmentConfig:
    """Testes para configuração de ambiente"""
    
    def test_ensure_directories(self):
        """Testa criação de diretórios"""
        # Usar diretório temporário para teste
        with tempfile.TemporaryDirectory() as temp_dir:
            # Modificar temporariamente os caminhos
            original_cache = EnvironmentConfig.CACHE_DIR
            original_temp = EnvironmentConfig.TEMP_DIR
            original_hf = EnvironmentConfig.HF_CACHE_DIR
            
            EnvironmentConfig.CACHE_DIR = os.path.join(temp_dir, "cache")
            EnvironmentConfig.TEMP_DIR = os.path.join(temp_dir, "temp")
            EnvironmentConfig.HF_CACHE_DIR = os.path.join(temp_dir, "hf_cache")
            
            # Executar função
            EnvironmentConfig.ensure_directories()
            
            # Verificar se diretórios foram criados
            assert os.path.exists(EnvironmentConfig.CACHE_DIR)
            assert os.path.exists(EnvironmentConfig.TEMP_DIR)
            assert os.path.exists(EnvironmentConfig.HF_CACHE_DIR)
            
            # Restaurar valores originais
            EnvironmentConfig.CACHE_DIR = original_cache
            EnvironmentConfig.TEMP_DIR = original_temp
            EnvironmentConfig.HF_CACHE_DIR = original_hf

class TestAppConstants:
    """Testes para constantes da aplicação"""
    
    def test_app_constants(self):
        """Testa constantes básicas"""
        assert AppConstants.APP_NAME == "CrisisComm"
        assert AppConstants.APP_VERSION == "1.0.0"
        
        # Verifica formatos suportados
        assert "mp3" in AppConstants.SUPPORTED_AUDIO_FORMATS
        assert "jpg" in AppConstants.SUPPORTED_IMAGE_FORMATS
        assert "mp4" in AppConstants.SUPPORTED_VIDEO_FORMATS
    
    def test_status_codes(self):
        """Testa códigos de status"""
        assert AppConstants.STATUS_SUCCESS == "success"
        assert AppConstants.STATUS_ERROR == "error"
        assert AppConstants.STATUS_WARNING == "warning"
        assert AppConstants.STATUS_INFO == "info"

# Função para executar todos os testes
def run_tests():
    """Executa todos os testes"""
    pytest.main([__file__, "-v"])

if __name__ == "__main__":
    run_tests()
