"""
CrisisComm - Comunicador de Emergência Multilíngue
Aplicativo desenvolvido com Streamlit e Gemma 3n para comunicação em situações de crise
"""

import streamlit as st
import torch
import numpy as np
import cv2
import librosa
import tempfile
import os
from PIL import Image
import io
import base64
from datetime import datetime
import json
import logging
from typing import Dict, List, Optional, Tuple
import asyncio
from transformers import AutoProcessor, Gemma3nForConditionalGeneration
import warnings
warnings.filterwarnings("ignore")

# Configuração da página
st.set_page_config(
    page_title="CrisisComm - Comunicador de Emergência",
    page_icon="🚨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CrisisCommApp:
    """Classe principal do aplicativo CrisisComm"""
    
    def __init__(self):
        self.model = None
        self.processor = None
        self.supported_languages = {
            "en": "English",
            "es": "Español", 
            "fr": "Français",
            "de": "Deutsch",
            "pt": "Português",
            "ar": "العربية",
            "zh": "中文",
            "ja": "日本語",
            "ko": "한국어",
            "hi": "हिन्दी",
            "ru": "Русский",
            "it": "Italiano"
        }
        self.emergency_templates = {
            "medical": "Emergência médica: {description}. Localização: {location}. Urgência: {urgency}",
            "fire": "Incêndio reportado: {description}. Localização: {location}. Extensão: {extent}",
            "natural_disaster": "Desastre natural: {description}. Localização: {location}. Situação: {status}",
            "accident": "Acidente: {description}. Localização: {location}. Vítimas: {casualties}",
            "security": "Emergência de segurança: {description}. Localização: {location}. Risco: {risk_level}"
        }
        
    @st.cache_resource
    def load_model(_self):
        """Carrega o modelo Gemma 3n com cache"""
        try:
            with st.spinner("🔄 Carregando modelo Gemma 3n... Isso pode levar alguns minutos na primeira vez."):
                # Configuração otimizada para dispositivos com recursos limitados
                model = Gemma3nForConditionalGeneration.from_pretrained(
                    "google/gemma-3n-e2b-it",
                    device_map="auto" if torch.cuda.is_available() else "cpu",
                    torch_dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float32,
                    use_cache=True,
                    low_cpu_mem_usage=True
                )
                
                processor = AutoProcessor.from_pretrained("google/gemma-3n-e2b-it")
                
                st.success("✅ Modelo carregado com sucesso!")
                return model, processor
                
        except Exception as e:
            st.error(f"❌ Erro ao carregar modelo: {str(e)}")
            st.info("💡 Dica: Certifique-se de ter acesso ao modelo Gemma 3n no Hugging Face")
            return None, None
    
    def emergency_translator(self, text: str, source_lang: str, target_lang: str) -> str:
        """Traduz texto de emergência entre idiomas"""
        if not self.model or not self.processor:
            return "Modelo não carregado"
            
        try:
            # Template de prompt para tradução de emergência
            prompt = f"""
            Traduza a seguinte mensagem de emergência de {self.supported_languages.get(source_lang, source_lang)} 
            para {self.supported_languages.get(target_lang, target_lang)}. 
            Mantenha o tom urgente e todas as informações críticas:
            
            Texto original: {text}
            
            Tradução:
            """
            
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "Você é um tradutor especializado em comunicações de emergência. Traduza com precisão mantendo o tom urgente."}]
                },
                {
                    "role": "user", 
                    "content": [{"type": "text", "text": prompt}]
                }
            ]
            
            inputs = self.processor.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.model.device)
            
            with torch.inference_mode():
                generation = self.model.generate(
                    **inputs,
                    max_new_tokens=200,
                    do_sample=False,
                    temperature=0.1
                )
                
            input_len = inputs["input_ids"].shape[-1]
            decoded = self.processor.decode(generation[0][input_len:], skip_special_tokens=True)
            
            return decoded.strip()
            
        except Exception as e:
            logger.error(f"Erro na tradução: {str(e)}")
            return f"Erro na tradução: {str(e)}"
    
    def analyze_damage_image(self, image: Image.Image) -> Dict:
        """Analisa imagem para avaliação de danos"""
        if not self.model or not self.processor:
            return {"error": "Modelo não carregado"}
            
        try:
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "Você é um especialista em avaliação de danos em emergências. Analise a imagem e forneça um relatório estruturado."}]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": "Analise esta imagem e forneça: 1) Tipo de dano, 2) Severidade (baixa/média/alta), 3) Riscos identificados, 4) Ações recomendadas"}
                    ]
                }
            ]
            
            inputs = self.processor.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.model.device)
            
            with torch.inference_mode():
                generation = self.model.generate(
                    **inputs,
                    max_new_tokens=300,
                    do_sample=False,
                    temperature=0.1
                )
                
            input_len = inputs["input_ids"].shape[-1]
            analysis = self.processor.decode(generation[0][input_len:], skip_special_tokens=True)
            
            return {
                "analysis": analysis.strip(),
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Erro na análise de imagem: {str(e)}")
            return {"error": f"Erro na análise: {str(e)}"}
    
    def transcribe_audio(self, audio_data: bytes) -> str:
        """Transcreve áudio para texto (simulado - requer implementação específica)"""
        # Nota: Esta é uma implementação simulada
        # Em produção, seria necessário usar a capacidade de áudio do Gemma 3n
        return "Transcrição de áudio não implementada nesta versão demo"
    
    def generate_situation_report(self, data: Dict) -> str:
        """Gera relatório de situação baseado nos dados coletados"""
        if not self.model or not self.processor:
            return "Modelo não carregado"
            
        try:
            report_prompt = f"""
            Gere um relatório de situação de emergência baseado nos seguintes dados:
            
            Tipo de emergência: {data.get('emergency_type', 'Não especificado')}
            Localização: {data.get('location', 'Não especificada')}
            Descrição: {data.get('description', 'Não fornecida')}
            Análise de imagem: {data.get('image_analysis', 'Não disponível')}
            Urgência: {data.get('urgency', 'Média')}
            
            Formate como um relatório profissional de emergência com:
            1. Resumo executivo
            2. Situação atual
            3. Riscos identificados
            4. Ações recomendadas
            5. Recursos necessários
            """
            
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "Você é um coordenador de emergências experiente. Gere relatórios claros e acionáveis."}]
                },
                {
                    "role": "user",
                    "content": [{"type": "text", "text": report_prompt}]
                }
            ]
            
            inputs = self.processor.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.model.device)
            
            with torch.inference_mode():
                generation = self.model.generate(
                    **inputs,
                    max_new_tokens=500,
                    do_sample=False,
                    temperature=0.1
                )
                
            input_len = inputs["input_ids"].shape[-1]
            report = self.processor.decode(generation[0][input_len:], skip_special_tokens=True)
            
            return report.strip()
            
        except Exception as e:
            logger.error(f"Erro na geração do relatório: {str(e)}")
            return f"Erro na geração do relatório: {str(e)}"

def main():
    """Função principal do aplicativo"""
    
    # Inicialização da aplicação
    if 'app' not in st.session_state:
        st.session_state.app = CrisisCommApp()
    
    app = st.session_state.app
    
    # Carregamento do modelo
    if app.model is None or app.processor is None:
        app.model, app.processor = app.load_model()
    
    # Interface principal
    st.title("🚨 CrisisComm - Comunicador de Emergência")
    st.markdown("### Sistema de Comunicação Multilíngue para Situações de Crise")
    
    # Sidebar com configurações
    with st.sidebar:
        st.header("⚙️ Configurações")
        
        # Status do modelo
        if app.model and app.processor:
            st.success("✅ Modelo Gemma 3n carregado")
        else:
            st.error("❌ Modelo não carregado")
            
        st.markdown("---")
        
        # Configurações de idioma
        st.subheader("🌍 Idiomas")
        source_lang = st.selectbox(
            "Idioma de origem:",
            options=list(app.supported_languages.keys()),
            format_func=lambda x: f"{app.supported_languages[x]} ({x})",
            index=0
        )
        
        target_lang = st.selectbox(
            "Idioma de destino:",
            options=list(app.supported_languages.keys()),
            format_func=lambda x: f"{app.supported_languages[x]} ({x})",
            index=1
        )
        
        st.markdown("---")
        
        # Tipo de emergência
        st.subheader("🚨 Tipo de Emergência")
        emergency_type = st.selectbox(
            "Selecione o tipo:",
            options=list(app.emergency_templates.keys()),
            format_func=lambda x: x.replace("_", " ").title()
        )
    
    # Abas principais
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🗣️ Tradutor de Emergência",
        "📸 Análise de Imagem",
        "🎤 Transcrição de Áudio",
        "📋 Relatório de Situação",
        "📚 Exemplos de Demonstração"
    ])
    
    with tab1:
        st.header("🗣️ Tradutor de Emergência Multilíngue")

        col1, col2 = st.columns(2)

        with col1:
            st.subheader(f"📝 Texto em {app.supported_languages[source_lang]}")
            input_text = st.text_area(
                "Digite a mensagem de emergência:",
                height=150,
                placeholder="Ex: Preciso de ajuda médica urgente. Localização: Rua das Flores, 123. Vítima inconsciente."
            )

            if st.button("🔄 Traduzir", type="primary"):
                if input_text.strip():
                    with st.spinner("Traduzindo..."):
                        translation = app.emergency_translator(input_text, source_lang, target_lang)
                        st.session_state.last_translation = translation
                else:
                    st.warning("Por favor, digite uma mensagem para traduzir.")

        with col2:
            st.subheader(f"📄 Tradução em {app.supported_languages[target_lang]}")
            if 'last_translation' in st.session_state:
                st.text_area(
                    "Mensagem traduzida:",
                    value=st.session_state.last_translation,
                    height=150,
                    disabled=True
                )

                # Botão para copiar
                if st.button("📋 Copiar Tradução"):
                    st.success("Tradução copiada para a área de transferência!")
            else:
                st.info("A tradução aparecerá aqui após você inserir um texto.")

    with tab2:
        st.header("📸 Análise de Imagem para Avaliação de Danos")

        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("📤 Upload de Imagem")
            uploaded_file = st.file_uploader(
                "Selecione uma imagem:",
                type=['png', 'jpg', 'jpeg', 'webp'],
                help="Formatos suportados: PNG, JPG, JPEG, WebP"
            )

            if uploaded_file is not None:
                # Exibir imagem
                image = Image.open(uploaded_file)
                st.image(image, caption="Imagem carregada", use_column_width=True)

                if st.button("🔍 Analisar Danos", type="primary"):
                    with st.spinner("Analisando imagem..."):
                        analysis_result = app.analyze_damage_image(image)
                        st.session_state.image_analysis = analysis_result

        with col2:
            st.subheader("📊 Resultado da Análise")
            if 'image_analysis' in st.session_state:
                result = st.session_state.image_analysis
                if 'error' not in result:
                    st.success("✅ Análise concluída")
                    st.text_area(
                        "Relatório de danos:",
                        value=result.get('analysis', ''),
                        height=200,
                        disabled=True
                    )

                    # Informações adicionais
                    st.info(f"📅 Análise realizada em: {result.get('timestamp', 'N/A')}")
                else:
                    st.error(f"❌ {result['error']}")
            else:
                st.info("O resultado da análise aparecerá aqui após o upload e análise de uma imagem.")

    with tab3:
        st.header("🎤 Transcrição e Tradução de Áudio")

        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("🎵 Upload de Áudio")
            audio_file = st.file_uploader(
                "Selecione um arquivo de áudio:",
                type=['mp3', 'wav', 'm4a', 'flac'],
                help="Formatos suportados: MP3, WAV, M4A, FLAC"
            )

            if audio_file is not None:
                st.audio(audio_file, format='audio/wav')

                if st.button("🎯 Transcrever e Traduzir", type="primary"):
                    with st.spinner("Processando áudio..."):
                        # Ler dados do áudio
                        audio_bytes = audio_file.read()
                        transcription = app.transcribe_audio(audio_bytes)

                        # Traduzir transcrição se necessário
                        if source_lang != target_lang:
                            translation = app.emergency_translator(transcription, source_lang, target_lang)
                        else:
                            translation = transcription

                        st.session_state.audio_transcription = transcription
                        st.session_state.audio_translation = translation

        with col2:
            st.subheader("📝 Transcrição e Tradução")
            if 'audio_transcription' in st.session_state:
                st.text_area(
                    f"Transcrição ({app.supported_languages[source_lang]}):",
                    value=st.session_state.audio_transcription,
                    height=100,
                    disabled=True
                )

                if 'audio_translation' in st.session_state and source_lang != target_lang:
                    st.text_area(
                        f"Tradução ({app.supported_languages[target_lang]}):",
                        value=st.session_state.audio_translation,
                        height=100,
                        disabled=True
                    )
            else:
                st.info("A transcrição aparecerá aqui após o upload e processamento do áudio.")

    with tab4:
        st.header("📋 Gerador de Relatório de Situação")

        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("📝 Informações da Emergência")

            # Formulário para coleta de dados
            with st.form("emergency_form"):
                location = st.text_input(
                    "📍 Localização:",
                    placeholder="Ex: Rua das Flores, 123, Centro, São Paulo"
                )

                description = st.text_area(
                    "📄 Descrição da situação:",
                    height=100,
                    placeholder="Descreva detalhadamente a situação de emergência..."
                )

                urgency = st.selectbox(
                    "⚡ Nível de urgência:",
                    options=["Baixa", "Média", "Alta", "Crítica"]
                )

                casualties = st.text_input(
                    "🏥 Vítimas/Feridos:",
                    placeholder="Ex: 2 feridos leves, 1 grave"
                )

                resources_needed = st.text_area(
                    "🚑 Recursos necessários:",
                    height=80,
                    placeholder="Ex: Ambulância, bombeiros, equipamento de resgate..."
                )

                submitted = st.form_submit_button("📊 Gerar Relatório", type="primary")

                if submitted:
                    if location and description:
                        # Coletar dados para o relatório
                        report_data = {
                            'emergency_type': emergency_type,
                            'location': location,
                            'description': description,
                            'urgency': urgency,
                            'casualties': casualties,
                            'resources_needed': resources_needed,
                            'image_analysis': st.session_state.get('image_analysis', {}).get('analysis', 'Não disponível'),
                            'timestamp': datetime.now().isoformat()
                        }

                        with st.spinner("Gerando relatório..."):
                            report = app.generate_situation_report(report_data)
                            st.session_state.situation_report = report
                            st.session_state.report_data = report_data
                    else:
                        st.error("Por favor, preencha pelo menos a localização e descrição.")

        with col2:
            st.subheader("📄 Relatório Gerado")
            if 'situation_report' in st.session_state:
                st.text_area(
                    "Relatório de situação:",
                    value=st.session_state.situation_report,
                    height=400,
                    disabled=True
                )

                # Botões de ação
                col_btn1, col_btn2 = st.columns(2)
                with col_btn1:
                    if st.button("📋 Copiar Relatório"):
                        st.success("Relatório copiado!")

                with col_btn2:
                    if st.button("📧 Exportar"):
                        # Criar dados para download
                        report_json = {
                            'report': st.session_state.situation_report,
                            'data': st.session_state.get('report_data', {}),
                            'generated_at': datetime.now().isoformat()
                        }

                        st.download_button(
                            label="💾 Baixar JSON",
                            data=json.dumps(report_json, indent=2, ensure_ascii=False),
                            file_name=f"relatorio_emergencia_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                            mime="application/json"
                        )
            else:
                st.info("O relatório aparecerá aqui após preencher o formulário e clicar em 'Gerar Relatório'.")

    with tab5:
        st.header("📚 Exemplos de Demonstração")

        # Importar e exibir exemplos
        try:
            from demo_examples import show_demo_data
            show_demo_data()
        except ImportError:
            st.error("❌ Módulo de exemplos não encontrado. Certifique-se de que demo_examples.py está no mesmo diretório.")
        except Exception as e:
            st.error(f"❌ Erro ao carregar exemplos: {str(e)}")

    # Footer
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666;'>
            🚨 <strong>CrisisComm</strong> - Desenvolvido com Streamlit e Gemma 3n<br>
            💡 Sistema offline para comunicação em emergências<br>
            🔒 Seus dados permanecem privados e seguros
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
