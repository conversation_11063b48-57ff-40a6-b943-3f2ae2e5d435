import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../../core/theme/app_theme.dart';
import '../pages/home_page.dart';

/// Quick actions floating action bar with contextual actions
class QuickActionsBar extends StatefulWidget {
  final TranslationMode currentMode;
  final VoidCallback onQuickTranslate;
  final VoidCallback onVoiceInput;
  final VoidCallback onCameraCapture;
  final VoidCallback onFileUpload;

  const QuickActionsBar({
    super.key,
    required this.currentMode,
    required this.onQuickTranslate,
    required this.onVoiceInput,
    required this.onCameraCapture,
    required this.onFileUpload,
  });

  @override
  State<QuickActionsBar> createState() => _QuickActionsBarState();
}

class _QuickActionsBarState extends State<QuickActionsBar>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _controller.forward();
  }

  @override
  void didUpdateWidget(QuickActionsBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentMode != widget.currentMode) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final actions = _getActionsForMode(widget.currentMode);
    
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              height: 60,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(width: 8),
                  ...actions.asMap().entries.map((entry) {
                    final index = entry.key;
                    final action = entry.value;
                    
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _QuickActionButton(
                            icon: action.icon,
                            label: action.label,
                            onPressed: action.onPressed,
                            isPrimary: action.isPrimary,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                  const SizedBox(width: 8),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  List<QuickAction> _getActionsForMode(TranslationMode mode) {
    switch (mode) {
      case TranslationMode.text:
        return [
          QuickAction(
            icon: Icons.paste,
            label: 'Colar',
            onPressed: widget.onQuickTranslate,
            isPrimary: false,
          ),
          QuickAction(
            icon: Icons.translate,
            label: 'Traduzir',
            onPressed: widget.onQuickTranslate,
            isPrimary: true,
          ),
          QuickAction(
            icon: Icons.mic,
            label: 'Voz',
            onPressed: widget.onVoiceInput,
            isPrimary: false,
          ),
        ];
        
      case TranslationMode.image:
        return [
          QuickAction(
            icon: Icons.camera_alt,
            label: 'Câmera',
            onPressed: widget.onCameraCapture,
            isPrimary: true,
          ),
          QuickAction(
            icon: Icons.photo_library,
            label: 'Galeria',
            onPressed: widget.onFileUpload,
            isPrimary: false,
          ),
          QuickAction(
            icon: Icons.translate,
            label: 'Traduzir',
            onPressed: widget.onQuickTranslate,
            isPrimary: false,
          ),
        ];
        
      case TranslationMode.audio:
        return [
          QuickAction(
            icon: Icons.mic,
            label: 'Gravar',
            onPressed: widget.onVoiceInput,
            isPrimary: true,
          ),
          QuickAction(
            icon: Icons.upload_file,
            label: 'Arquivo',
            onPressed: widget.onFileUpload,
            isPrimary: false,
          ),
          QuickAction(
            icon: Icons.translate,
            label: 'Traduzir',
            onPressed: widget.onQuickTranslate,
            isPrimary: false,
          ),
        ];
        
      case TranslationMode.video:
        return [
          QuickAction(
            icon: Icons.videocam,
            label: 'Gravar',
            onPressed: widget.onCameraCapture,
            isPrimary: true,
          ),
          QuickAction(
            icon: Icons.video_library,
            label: 'Biblioteca',
            onPressed: widget.onFileUpload,
            isPrimary: false,
          ),
          QuickAction(
            icon: Icons.translate,
            label: 'Traduzir',
            onPressed: widget.onQuickTranslate,
            isPrimary: false,
          ),
        ];
    }
  }
}

class _QuickActionButton extends StatefulWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final bool isPrimary;

  const _QuickActionButton({
    required this.icon,
    required this.label,
    required this.onPressed,
    required this.isPrimary,
  });

  @override
  State<_QuickActionButton> createState() => _QuickActionButtonState();
}

class _QuickActionButtonState extends State<_QuickActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _pressController;
  late Animation<double> _pressAnimation;

  @override
  void initState() {
    super.initState();
    _pressController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _pressAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _pressAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pressAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _pressController.forward().then((_) {
                    _pressController.reverse();
                  });
                  widget.onPressed();
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: widget.isPrimary ? 20 : 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: widget.isPrimary
                        ? Colors.white.withOpacity(0.2)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: widget.isPrimary
                        ? null
                        : Border.all(
                            color: Colors.white.withOpacity(0.3),
                          ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.icon,
                        color: Colors.white,
                        size: widget.isPrimary ? 20 : 18,
                      ),
                      if (widget.isPrimary) ...[
                        const SizedBox(width: 8),
                        Text(
                          widget.label,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class QuickAction {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final bool isPrimary;

  const QuickAction({
    required this.icon,
    required this.label,
    required this.onPressed,
    this.isPrimary = false,
  });
}
