import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

import '../utils/logger.dart';

/// Performance monitoring and optimization service
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  static PerformanceService get instance => _instance;
  PerformanceService._internal();

  final Queue<PerformanceMetric> _metrics = Queue<PerformanceMetric>();
  final Map<String, Timer> _activeTimers = {};
  final Map<String, int> _operationCounts = {};
  final Map<String, Duration> _totalDurations = {};
  
  static const int _maxMetricsHistory = 1000;
  
  bool _isMonitoring = false;
  DateTime? _sessionStart;

  /// Start performance monitoring
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _sessionStart = DateTime.now();
    Logger.info('Performance monitoring started');
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    _isMonitoring = false;
    _activeTimers.clear();
    Logger.info('Performance monitoring stopped');
  }

  /// Record model initialization performance
  void recordModelInitialization(String modelName) {
    if (!_isMonitoring) return;
    
    _recordMetric(PerformanceMetric(
      operation: 'model_initialization',
      category: 'gemma',
      metadata: {'model_name': modelName},
      timestamp: DateTime.now(),
    ));
  }

  /// Record translation performance
  void recordTranslation({
    required String sourceLanguage,
    required String targetLanguage,
    required int textLength,
    required Duration processingTime,
    String? translationType,
  }) {
    if (!_isMonitoring) return;
    
    final metric = PerformanceMetric(
      operation: 'translation',
      category: translationType ?? 'text',
      duration: processingTime,
      metadata: {
        'source_language': sourceLanguage,
        'target_language': targetLanguage,
        'text_length': textLength,
        'tokens_per_second': textLength / processingTime.inSeconds,
      },
      timestamp: DateTime.now(),
    );
    
    _recordMetric(metric);
    _updateOperationStats('translation', processingTime);
  }

  /// Record cache operation performance
  void recordCacheOperation({
    required String operation, // 'hit', 'miss', 'write'
    required Duration duration,
    int? dataSize,
  }) {
    if (!_isMonitoring) return;
    
    _recordMetric(PerformanceMetric(
      operation: 'cache_$operation',
      category: 'cache',
      duration: duration,
      metadata: {
        'data_size': dataSize,
      },
      timestamp: DateTime.now(),
    ));
  }

  /// Record memory usage
  void recordMemoryUsage({
    required int usedMemoryMB,
    required int totalMemoryMB,
    String? context,
  }) {
    if (!_isMonitoring) return;
    
    _recordMetric(PerformanceMetric(
      operation: 'memory_usage',
      category: 'system',
      metadata: {
        'used_memory_mb': usedMemoryMB,
        'total_memory_mb': totalMemoryMB,
        'usage_percentage': (usedMemoryMB / totalMemoryMB * 100).round(),
        'context': context,
      },
      timestamp: DateTime.now(),
    ));
  }

  /// Record network operation
  void recordNetworkOperation({
    required String operation,
    required Duration duration,
    required bool success,
    int? dataSize,
    String? errorMessage,
  }) {
    if (!_isMonitoring) return;
    
    _recordMetric(PerformanceMetric(
      operation: 'network_$operation',
      category: 'network',
      duration: duration,
      metadata: {
        'success': success,
        'data_size': dataSize,
        'error_message': errorMessage,
      },
      timestamp: DateTime.now(),
    ));
  }

  /// Start timing an operation
  void startTimer(String operationId) {
    if (!_isMonitoring) return;
    
    _activeTimers[operationId] = Timer(Duration.zero, () {});
    Logger.debug('Started timer for: $operationId');
  }

  /// Stop timing an operation and record the result
  Duration? stopTimer(String operationId, {
    String? category,
    Map<String, dynamic>? metadata,
  }) {
    if (!_isMonitoring) return null;
    
    final timer = _activeTimers.remove(operationId);
    if (timer == null) {
      Logger.warning('No active timer found for: $operationId');
      return null;
    }
    
    final duration = DateTime.now().difference(
      DateTime.now().subtract(timer.tick * const Duration(microseconds: 1))
    );
    
    _recordMetric(PerformanceMetric(
      operation: operationId,
      category: category ?? 'general',
      duration: duration,
      metadata: metadata,
      timestamp: DateTime.now(),
    ));
    
    Logger.debug('Stopped timer for: $operationId (${duration.inMilliseconds}ms)');
    return duration;
  }

  /// Get performance metrics summary
  Map<String, dynamic> getMetrics() {
    final now = DateTime.now();
    final sessionDuration = _sessionStart != null 
        ? now.difference(_sessionStart!)
        : Duration.zero;

    return {
      'session_duration_minutes': sessionDuration.inMinutes,
      'total_operations': _metrics.length,
      'operations_per_minute': _metrics.length / (sessionDuration.inMinutes + 1),
      'operation_counts': Map.from(_operationCounts),
      'average_durations': _getAverageDurations(),
      'recent_metrics': _getRecentMetrics(50),
      'performance_summary': _getPerformanceSummary(),
    };
  }

  /// Get metrics for a specific operation type
  List<PerformanceMetric> getMetricsForOperation(String operation) {
    return _metrics.where((m) => m.operation == operation).toList();
  }

  /// Get metrics for a specific category
  List<PerformanceMetric> getMetricsForCategory(String category) {
    return _metrics.where((m) => m.category == category).toList();
  }

  /// Get performance trends over time
  Map<String, dynamic> getPerformanceTrends() {
    final translationMetrics = getMetricsForOperation('translation');
    
    if (translationMetrics.isEmpty) {
      return {'trends': 'No data available'};
    }

    // Calculate trends for the last hour
    final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));
    final recentMetrics = translationMetrics
        .where((m) => m.timestamp.isAfter(oneHourAgo))
        .toList();

    if (recentMetrics.isEmpty) {
      return {'trends': 'No recent data'};
    }

    // Group by 10-minute intervals
    final intervals = <DateTime, List<PerformanceMetric>>{};
    for (final metric in recentMetrics) {
      final intervalStart = DateTime(
        metric.timestamp.year,
        metric.timestamp.month,
        metric.timestamp.day,
        metric.timestamp.hour,
        (metric.timestamp.minute ~/ 10) * 10,
      );
      intervals.putIfAbsent(intervalStart, () => []).add(metric);
    }

    // Calculate average performance for each interval
    final trendData = intervals.entries.map((entry) {
      final avgDuration = entry.value
          .map((m) => m.duration?.inMilliseconds ?? 0)
          .reduce((a, b) => a + b) / entry.value.length;
      
      return {
        'timestamp': entry.key.toIso8601String(),
        'average_duration_ms': avgDuration.round(),
        'operation_count': entry.value.length,
      };
    }).toList();

    return {
      'trend_data': trendData,
      'total_operations': recentMetrics.length,
      'time_range': 'Last 1 hour',
    };
  }

  /// Clear all metrics
  void clearMetrics() {
    _metrics.clear();
    _operationCounts.clear();
    _totalDurations.clear();
    _activeTimers.clear();
    Logger.info('Performance metrics cleared');
  }

  /// Export metrics to JSON
  Map<String, dynamic> exportMetrics() {
    return {
      'export_timestamp': DateTime.now().toIso8601String(),
      'session_start': _sessionStart?.toIso8601String(),
      'metrics': _metrics.map((m) => m.toJson()).toList(),
      'summary': getMetrics(),
    };
  }

  // Private helper methods
  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // Maintain maximum history size
    while (_metrics.length > _maxMetricsHistory) {
      _metrics.removeFirst();
    }
    
    // Update operation counts
    _operationCounts[metric.operation] = 
        (_operationCounts[metric.operation] ?? 0) + 1;
  }

  void _updateOperationStats(String operation, Duration duration) {
    _totalDurations[operation] = 
        (_totalDurations[operation] ?? Duration.zero) + duration;
  }

  Map<String, double> _getAverageDurations() {
    final averages = <String, double>{};
    
    for (final entry in _totalDurations.entries) {
      final count = _operationCounts[entry.key] ?? 1;
      averages[entry.key] = entry.value.inMilliseconds / count;
    }
    
    return averages;
  }

  List<Map<String, dynamic>> _getRecentMetrics(int count) {
    return _metrics
        .toList()
        .reversed
        .take(count)
        .map((m) => m.toJson())
        .toList();
  }

  Map<String, dynamic> _getPerformanceSummary() {
    final translationMetrics = getMetricsForOperation('translation');
    
    if (translationMetrics.isEmpty) {
      return {'status': 'No translation data'};
    }

    final durations = translationMetrics
        .where((m) => m.duration != null)
        .map((m) => m.duration!.inMilliseconds)
        .toList();

    if (durations.isEmpty) {
      return {'status': 'No duration data'};
    }

    durations.sort();
    
    final avg = durations.reduce((a, b) => a + b) / durations.length;
    final median = durations[durations.length ~/ 2];
    final p95 = durations[(durations.length * 0.95).round() - 1];
    
    return {
      'translation_performance': {
        'average_ms': avg.round(),
        'median_ms': median,
        'p95_ms': p95,
        'min_ms': durations.first,
        'max_ms': durations.last,
        'total_translations': durations.length,
      },
      'performance_rating': _getPerformanceRating(avg),
    };
  }

  String _getPerformanceRating(double avgMs) {
    if (avgMs < 500) return 'excellent';
    if (avgMs < 1000) return 'good';
    if (avgMs < 2000) return 'fair';
    return 'poor';
  }
}

/// Performance metric data model
class PerformanceMetric {
  final String operation;
  final String category;
  final Duration? duration;
  final Map<String, dynamic>? metadata;
  final DateTime timestamp;

  PerformanceMetric({
    required this.operation,
    required this.category,
    this.duration,
    this.metadata,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'category': category,
      'duration_ms': duration?.inMilliseconds,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      operation: json['operation'],
      category: json['category'],
      duration: json['duration_ms'] != null 
          ? Duration(milliseconds: json['duration_ms'])
          : null,
      metadata: json['metadata'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
