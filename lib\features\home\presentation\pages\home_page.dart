import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/services/analytics_service.dart';
import '../widgets/translation_mode_selector.dart';
import '../widgets/language_selector.dart';
import '../widgets/translation_input_area.dart';
import '../widgets/translation_result_area.dart';
import '../widgets/quick_actions_bar.dart';
import '../widgets/status_indicator.dart';
import '../../translation/presentation/pages/text_translation_page.dart';
import '../../translation/presentation/pages/image_translation_page.dart';
import '../../translation/presentation/pages/audio_translation_page.dart';
import '../../translation/presentation/pages/video_translation_page.dart';
import '../../settings/presentation/pages/settings_page.dart';
import '../../history/presentation/pages/history_page.dart';

/// Main home page with multimodal translation interface
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  final List<TranslationMode> _translationModes = [
    TranslationMode.text,
    TranslationMode.image,
    TranslationMode.audio,
    TranslationMode.video,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _translationModes.length, vsync: this);
    _tabController.addListener(_onTabChanged);
    
    // Track page view
    AnalyticsService.instance.trackUserEngagement(
      action: 'page_view',
      feature: 'home_page',
    );
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _currentIndex = _tabController.index;
      });
      
      // Track mode change
      AnalyticsService.instance.trackFeatureUsage(
        featureName: 'translation_mode_change',
        parameters: {
          'mode': _translationModes[_currentIndex].name,
          'index': _currentIndex,
        },
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gemmaInitialization = ref.watch(gemmaInitializationProvider);
    final languagePreferences = ref.watch(languagePreferencesProvider);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: AppTheme.primaryGradientDecoration,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'SimulTrans AI',
                                    style: theme.textTheme.headlineMedium?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Tradutor Multimodal Inteligente',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: Colors.white.withOpacity(0.9),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Status Indicator
                            StatusIndicator(
                              gemmaStatus: gemmaInitialization,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.history, color: Colors.white),
                onPressed: () => _navigateToHistory(context),
              ),
              IconButton(
                icon: const Icon(Icons.settings, color: Colors.white),
                onPressed: () => _navigateToSettings(context),
              ),
            ],
          ),

          // Language Selector
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(16),
              child: LanguageSelector(
                sourceLanguage: languagePreferences.sourceLanguage,
                targetLanguage: languagePreferences.targetLanguage,
                onSourceLanguageChanged: (language) {
                  ref.read(languagePreferencesProvider.notifier)
                      .setSourceLanguage(language);
                },
                onTargetLanguageChanged: (language) {
                  ref.read(languagePreferencesProvider.notifier)
                      .setTargetLanguage(language);
                },
                onSwapLanguages: () {
                  final current = languagePreferences;
                  if (current.sourceLanguage != 'auto') {
                    ref.read(languagePreferencesProvider.notifier)
                        .setSourceLanguage(current.targetLanguage);
                    ref.read(languagePreferencesProvider.notifier)
                        .setTargetLanguage(current.sourceLanguage);
                  }
                },
              ),
            ),
          ),

          // Translation Mode Selector
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: TranslationModeSelector(
                currentMode: _translationModes[_currentIndex],
                onModeChanged: (mode) {
                  final index = _translationModes.indexOf(mode);
                  _tabController.animateTo(index);
                },
              ),
            ),
          ),

          // Translation Interface
          SliverFillRemaining(
            child: Container(
              margin: const EdgeInsets.all(16),
              child: gemmaInitialization.when(
                data: (isInitialized) {
                  if (!isInitialized) {
                    return _buildErrorState(
                      'Gemma 3N não foi inicializado',
                      'Tente reiniciar o aplicativo',
                    );
                  }
                  
                  return TabBarView(
                    controller: _tabController,
                    children: [
                      TextTranslationPage(),
                      ImageTranslationPage(),
                      AudioTranslationPage(),
                      VideoTranslationPage(),
                    ],
                  );
                },
                loading: () => _buildLoadingState(),
                error: (error, stack) => _buildErrorState(
                  'Erro na inicialização',
                  error.toString(),
                ),
              ),
            ),
          ),
        ],
      ),

      // Quick Actions FAB
      floatingActionButton: _buildQuickActionsFAB(context),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Inicializando Gemma 3N...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(String title, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              ref.read(gemmaInitializationProvider.notifier).reinitialize();
            },
            child: const Text('Tentar Novamente'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsFAB(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: QuickActionsBar(
        currentMode: _translationModes[_currentIndex],
        onQuickTranslate: () => _performQuickTranslation(),
        onVoiceInput: () => _startVoiceInput(),
        onCameraCapture: () => _startCameraCapture(),
        onFileUpload: () => _startFileUpload(),
      ),
    );
  }

  void _performQuickTranslation() {
    // Implement quick translation based on current mode
    AnalyticsService.instance.trackFeatureUsage(
      featureName: 'quick_translation',
      parameters: {'mode': _translationModes[_currentIndex].name},
    );
  }

  void _startVoiceInput() {
    // Navigate to audio translation or start voice input
    if (_currentIndex != 2) { // Not already on audio tab
      _tabController.animateTo(2);
    }
    AnalyticsService.instance.trackFeatureUsage(featureName: 'voice_input');
  }

  void _startCameraCapture() {
    // Navigate to image translation or start camera
    if (_currentIndex != 1) { // Not already on image tab
      _tabController.animateTo(1);
    }
    AnalyticsService.instance.trackFeatureUsage(featureName: 'camera_capture');
  }

  void _startFileUpload() {
    // Show file picker based on current mode
    AnalyticsService.instance.trackFeatureUsage(
      featureName: 'file_upload',
      parameters: {'mode': _translationModes[_currentIndex].name},
    );
  }

  void _navigateToHistory(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const HistoryPage()),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SettingsPage()),
    );
  }
}

/// Translation modes enum
enum TranslationMode {
  text('Texto', Icons.text_fields),
  image('Imagem', Icons.image),
  audio('Áudio', Icons.mic),
  video('Vídeo', Icons.videocam);

  const TranslationMode(this.label, this.icon);
  
  final String label;
  final IconData icon;
}
