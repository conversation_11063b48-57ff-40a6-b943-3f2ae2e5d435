import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import '../utils/logger.dart';

/// OCR result containing extracted text and metadata
class OCRResult {
  final String extractedText;
  final String detectedLanguage;
  final double confidence;
  final List<TextBlock> textBlocks;
  final Duration processingTime;
  final Map<String, dynamic> metadata;

  OCRResult({
    required this.extractedText,
    required this.detectedLanguage,
    required this.confidence,
    required this.textBlocks,
    required this.processingTime,
    required this.metadata,
  });
}

/// Individual text block with position information
class TextBlock {
  final String text;
  final double confidence;
  final Map<String, dynamic>? boundingBox;

  TextBlock({
    required this.text,
    required this.confidence,
    this.boundingBox,
  });
}

/// OCR service using Ollama for text extraction from images
class OCRService {
  static final OCRService _instance = OCRService._internal();
  static OCRService get instance => _instance;
  OCRService._internal();

  http.Client? _httpClient;
  bool _isInitialized = false;
  String _currentModel = '';
  String _ollamaHost = '';
  int _ocrTimeout = 900;

  bool get isInitialized => _isInitialized;

  /// Initialize the OCR service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load environment variables
      _currentModel = dotenv.env['OLLAMA_MODEL_NAME'] ?? 'gemma3n:e2b';
      _ollamaHost = dotenv.env['OLLAMA_HOST'] ?? 'http://localhost:11434';
      _ocrTimeout = int.tryParse(dotenv.env['OLLAMA_IMAGE_TIMEOUT'] ?? '900') ?? 900;

      // Initialize HTTP client
      _httpClient = http.Client();

      // Test connection
      await _testConnection();

      _isInitialized = true;
      if (kDebugMode) {
        print('✅ OCR service initialized successfully');
        print('🔍 Using model: $_currentModel for OCR');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize OCR service: $e');
      }
      throw Exception('Failed to initialize OCR service: $e');
    }
  }

  /// Test connection to Ollama server
  Future<void> _testConnection() async {
    try {
      final response = await _httpClient!
          .get(Uri.parse('$_ollamaHost/api/tags'))
          .timeout(Duration(seconds: 30));

      if (response.statusCode != 200) {
        throw Exception('Ollama server not responding. Status: ${response.statusCode}');
      }

      final data = jsonDecode(response.body);
      final models = data['models'] as List<dynamic>? ?? [];
      final modelExists = models.any((model) => model['name'] == _currentModel);

      if (!modelExists) {
        throw Exception('Model $_currentModel not found in Ollama');
      }

    } catch (e) {
      throw Exception('Failed to connect to Ollama for OCR: $e');
    }
  }

  /// Extract text from image using OCR
  Future<OCRResult> extractText({
    required Uint8List imageBytes,
    String? expectedLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      // Convert image to base64
      final base64Image = base64Encode(imageBytes);

      final prompt = _buildOCRPrompt(
        expectedLanguage: expectedLanguage,
        additionalContext: additionalContext,
      );

      final response = await _performOCR(
        prompt: prompt,
        imageBase64: base64Image,
        timeout: Duration(seconds: _ocrTimeout),
      );

      final result = _parseOCRResponse(response);
      final duration = DateTime.now().difference(startTime);

      return OCRResult(
        extractedText: result['extracted_text'] ?? '',
        detectedLanguage: result['detected_language'] ?? 'auto',
        confidence: result['confidence']?.toDouble() ?? 0.8,
        textBlocks: _parseTextBlocks(result['text_blocks']),
        processingTime: duration,
        metadata: {
          'model': _currentModel,
          'method': 'ollama_ocr',
          'host': _ollamaHost,
          'processing_time_ms': duration.inMilliseconds,
          'image_size_bytes': imageBytes.length,
          'service': 'ocr_offline',
        },
      );

    } catch (e) {
      Logger.error('OCR extraction failed: $e');
      final duration = DateTime.now().difference(startTime);
      
      return OCRResult(
        extractedText: '',
        detectedLanguage: 'auto',
        confidence: 0.0,
        textBlocks: [],
        processingTime: duration,
        metadata: {
          'error': e.toString(),
          'service': 'ocr_offline',
        },
      );
    }
  }

  /// Perform OCR using Ollama API
  Future<String> _performOCR({
    required String prompt,
    required String imageBase64,
    required Duration timeout,
  }) async {
    final requestBody = {
      'model': _currentModel,
      'prompt': prompt,
      'images': [imageBase64],
      'stream': false,
      'options': {
        'temperature': 0.1, // Lower temperature for more accurate OCR
        'top_p': 0.9,
        'max_tokens': 1024, // More tokens for detailed OCR output
      },
    };

    final response = await _httpClient!
        .post(
          Uri.parse('$_ollamaHost/api/generate'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(requestBody),
        )
        .timeout(timeout);

    if (response.statusCode != 200) {
      throw Exception('Ollama OCR API error: ${response.statusCode} - ${response.body}');
    }

    final data = jsonDecode(response.body);
    return data['response'] ?? '';
  }

  /// Build OCR-specific prompt
  String _buildOCRPrompt({
    String? expectedLanguage,
    String? additionalContext,
  }) {
    final langInfo = expectedLanguage != null ? '\nExpected language: $expectedLanguage' : '';
    final contextInfo = additionalContext != null ? '\nContext: $additionalContext' : '';
    
    return '''You are an expert OCR (Optical Character Recognition) system. Your task is to extract ALL visible text from this image with maximum accuracy.

Instructions:
1. Carefully scan the entire image for any text, including:
   - Main text content
   - Signs and labels
   - Buttons and UI elements
   - Watermarks or small text
   - Numbers and symbols
   - Text in any orientation

2. Preserve the original formatting and structure
3. Detect the language of the extracted text
4. Provide confidence scores for text blocks$langInfo$contextInfo

Respond in this JSON format:
{
  "extracted_text": "all text found in the image, preserving line breaks and structure",
  "detected_language": "language code (e.g., 'en', 'pt', 'es')",
  "confidence": 0.95,
  "text_blocks": [
    {
      "text": "individual text block",
      "confidence": 0.98,
      "position": "description of position in image"
    }
  ]
}

Focus on accuracy and completeness. Extract every piece of readable text.''';
  }

  /// Parse OCR response from Ollama
  Map<String, dynamic> _parseOCRResponse(String response) {
    try {
      // Try to parse as JSON first
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        final jsonStr = jsonMatch.group(0)!;
        return jsonDecode(jsonStr);
      }
    } catch (e) {
      // If JSON parsing fails, treat as plain text
    }
    
    // Fallback: treat entire response as extracted text
    return {
      'extracted_text': response.trim(),
      'detected_language': 'auto',
      'confidence': 0.7,
      'text_blocks': [
        {
          'text': response.trim(),
          'confidence': 0.7,
          'position': 'unknown'
        }
      ]
    };
  }

  /// Parse text blocks from response
  List<TextBlock> _parseTextBlocks(dynamic textBlocksData) {
    if (textBlocksData is! List) return [];
    
    return textBlocksData.map<TextBlock>((block) {
      if (block is Map<String, dynamic>) {
        return TextBlock(
          text: block['text']?.toString() ?? '',
          confidence: block['confidence']?.toDouble() ?? 0.8,
          boundingBox: block['position'] != null ? {'position': block['position']} : null,
        );
      }
      return TextBlock(text: block.toString(), confidence: 0.7);
    }).toList();
  }

  /// Dispose resources
  void dispose() {
    _httpClient?.close();
    _httpClient = null;
    _isInitialized = false;
  }
}
